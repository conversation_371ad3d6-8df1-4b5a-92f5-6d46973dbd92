-- dbext:type=SQLITE:dbname=movie_rating.db

/* Delete the tables if they already exist */
drop table if exists Movie;
drop table if exists Reviewer;
drop table if exists Rating;

/* Create the schema for our tables */
create table Movie(
	mID int primary key, 
	title text, 
	year int, 
	director text
);
create table Reviewer(
	rID int primary key, 
	name text);

create table Rating(
	rID int, 
	mID int, 
	stars int, 
	ratingDate date,
	FOREIGN KEY (mID) references Movie(mID),
	FOREIGN KEY (rID) references Reviewer(rID)
);

/* Populate the tables with our data */
insert into Movie values(101, 'Gone with the Wind', 1939, '<PERSON>');
insert into Movie values(102, 'Star Wars', 1977, '<PERSON>');
insert into Movie values(103, 'The Sound of Music', 1965, '<PERSON>');
insert into Movie values(104, 'E.T.', 1982, '<PERSON>lberg');
insert into Movie values(105, '<PERSON>', 1997, '<PERSON>');
insert into Movie values(106, '<PERSON> White', 1937, null);
insert into Movie values(107, 'Ava<PERSON>', 2009, '<PERSON>');
insert into Movie values(108, 'Raiders of the Lost Ark', 1981, '<PERSON>');

insert into Reviewer values(201, '<PERSON>');
insert into Reviewer values(202, '<PERSON>');
insert into Reviewer values(203, '<PERSON>');
insert into Reviewer values(204, '<PERSON> Anderson');
insert into Reviewer values(205, '<PERSON> <PERSON>');
insert into Reviewer values(206, 'Elizabeth <PERSON>');
insert into Reviewer values(207, '<PERSON> Cameron');
insert into Reviewer values(208, 'Ashley White');

insert into Rating values(201, 101, 2, '2011-01-22');
insert into Rating values(201, 101, 4, '2011-01-27');
insert into Rating values(202, 106, 4, null);
insert into Rating values(203, 103, 2, '2011-01-20');
insert into Rating values(203, 108, 4, '2011-01-12');
insert into Rating values(203, 108, 2, '2011-01-30');
insert into Rating values(204, 101, 3, '2011-01-09');
insert into Rating values(205, 103, 3, '2011-01-27');
insert into Rating values(205, 104, 2, '2011-01-22');
insert into Rating values(205, 108, 4, null);
insert into Rating values(206, 107, 3, '2011-01-15');
insert into Rating values(206, 106, 5, '2011-01-19');
insert into Rating values(207, 107, 5, '2011-01-20');
insert into Rating values(208, 104, 3, '2011-01-02');

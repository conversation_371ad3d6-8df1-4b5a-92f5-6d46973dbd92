CREATE TABLE Person (
  name varchar(20) PRIMARY KEY,
  age INTEGER,
  city TEXT,
  gender TEXT,
  job TEXT
);

CREATE TABLE PersonFriend (
  name varchar(20),
  friend varchar(20),
  year INTEGER,
  <PERSON>OR<PERSON><PERSON><PERSON> (name) REFERENCES Person(name),
  <PERSON>OR<PERSON><PERSON><PERSON> (friend) REFERENCES Person(name)
);

INSERT INTO Person VALUES ('<PERSON>',25,'new york city','female','student');
INSERT INTO Person VALUES ('<PERSON>',35,'salt lake city','male','engineer');
INSERT INTO Person VALUES ('<PERSON>', 45,'austin','male','doctor');
 INSERT INTO Person VALUES ('<PERSON>',26,'chicago','female','student');

INSERT INTO PersonFriend VALUES ('<PERSON>','<PERSON>',10);
INSERT INTO PersonFriend VALUES ('<PERSON>','<PERSON>', 12);
INSERT INTO PersonFriend VALUES ('<PERSON>','<PERSON>', 5);
INSERT INTO PersonFriend VALUES ('<PERSON>','<PERSON>', 6);

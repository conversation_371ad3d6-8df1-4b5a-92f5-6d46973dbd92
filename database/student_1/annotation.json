{"label_id": null, "data": [{"nl": "Find all students who study in classroom 111. For each student list first and last name.\n", "id": 0}, {"nl": "For each classroom report the grade that is taught in it. Report just the classroom number and the grade number.\n", "id": 1}, {"nl": "Find all teachers who teach fifth grade. Report first and last name of the teachers and the room number.\n", "id": 2}, {"nl": "Find all students taught by OTHA MOYER. Output first and last names of students\n", "id": 3}, {"nl": "For each teacher teaching grades K through 3, report the grade (s)he teaches. Each name has to be reported exactly once.\n", "id": 4}, {"nl": "Find all first-grade students who are NOT taught by OTHA MOYER. Report their first and last names\n", "id": 5}, {"nl": "Find and report the number of students taught by LORIA ONDERSMA.\n", "id": 6}, {"nl": "For each grade, report the number of classrooms in which it is taught and the toal number of students in the grade.\n", "id": 7}, {"nl": "Report the names of teachers who have between seven and eight students in their classes.\n", "id": 8}, {"nl": "For each kindergarden classroom, report the total number of students.\n", "id": 9}, {"nl": "Find the teacher name who teach(es) the largest number of students.\n", "id": 10}, {"nl": "Findallclassroomswhichhavefewerstudentsinthemthantheaverage number of students in a classroom in the school. Report the classroom numbers\n", "id": 11}, {"nl": "For each grade with more than one classroom, report the last name of the teacher who teachers the classroom with the largest number of students in the grade.\n", "id": 12}], "review_id": null}
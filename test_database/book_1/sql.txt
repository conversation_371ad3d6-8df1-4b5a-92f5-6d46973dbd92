SELECT TITLE, <PERSON><PERSON><PERSON> "YEAR", <PERSON>AM<PERSON> "Author name"
FROM BOOK NATURAL JOIN AUTHOR_BOOK, AUTHOR
WHERE AUTHOR_BOOK.AUTHOR=AUTHOR.IDAUTHOR
ORDER BY AÑO;

SELECT TITLE, AÑO
FROM BOOK
WHERE AÑO<'2000';

SELECT DISTINCT IDCLIENT, NAME
FROM CLIENT NATURAL JOIN ORDERS;
select distinct client.name, Client.IdClient
from Client, Orders where
Client.IdClient=Orders.IdClient;

SELECT IDCLIENT, NAME
FROM CLIENT NATURAL JOIN ORDERS NATURAL JOIN BOOKS_ORDER
WHERE ISBN='4554672899910';

SELECT IDCLIENT, NAME, TITLE
FROM CLIENT NATURAL JOIN ORDERS NATURAL JOIN BOOKS_ORDER NATURAL JOIN BOOK
WHERE NAME LIKE '%Jo%';

SELECT DISTINCT IDCLIENT, NAME
FROM CLIENT NATURAL JOIN ORDERS NATURAL JOIN BOOKS_ORDER NATURAL JOIN BOOK
WHERE BOOK.SALEPRICE>10;

SELECT IDCLIENT, NAME
FROM CLIENT
WHERE IDCLIENT=(
  SELECT IDCLIENT
  FROM ORDERS
  GROUP BY(IDCLIENT,DATEORDER)
  HAVING COUNT(DATEORDER)>1
);

SELECT IDCLIENT, NAME, DATEORDER
FROM CLIENT NATURAL JOIN ORDERS
WHERE DATEEXPED IS NULL;

SELECT DISTINCT IDCLIENT, NAME
FROM CLIENT
WHERE IDCLIENT NOT IN (
  SELECT IDCLIENT
  FROM ORDERS NATURAL JOIN BOOKS_ORDER NATURAL JOIN BOOK
  WHERE SALEPRICE < 10
);

SELECT TITLE, AÑO, SALEPRICE
FROM BOOK
WHERE AÑO<'2000' OR SALEPRICE>30;

SELECT TITLE, ISBN, SUM(AMOUNT)
FROM BOOKS_ORDER NATURAL JOIN BOOK
GROUP BY TITLE, ISBN
ORDER BY SUM(AMOUNT) DESC;

SELECT IDCLIENT, NAME, SUM(AMOUNT*SALEPRICE) "Total"
FROM CLIENT NATURAL JOIN ORDERS NATURAL JOIN BOOKS_ORDER NATURAL JOIN BOOK
GROUP BY IDCLIENT, NAME;

SELECT SUM(AMOUNT*(SALEPRICE-PURCHASEPRICE)) "Total"
FROM BOOKS_ORDER NATURAL JOIN BOOK;

SELECT IDORDER, SUM(AMOUNT*SALEPRICE) "TOTAL"
FROM BOOKS_ORDER NATURAL JOIN BOOK
GROUP BY IDORDER
HAVING SUM(AMOUNT*SALEPRICE)>100
ORDER BY IDORDER;

SELECT IDORDER, SUM(AMOUNT*SALEPRICE) "TOTAL"
FROM BOOKS_ORDER NATURAL JOIN BOOK
GROUP BY IDORDER
HAVING COUNT(TITLE)>1;

SELECT IDORDER, SUM(AMOUNT*SALEPRICE) "Total"
FROM BOOKS_ORDER NATURAL JOIN BOOK 
WHERE AMOUNT>4
GROUP BY IDORDER;

SELECT ISBN, TITLE, SALEPRICE
FROM BOOK
WHERE SALEPRICE IN (SELECT MAX(SALEPRICE) FROM BOOK);

SELECT ISBN, TITLE
FROM BOOK
WHERE(SALEPRICE-PURCHASEPRICE)<5 OR ISBN NOT IN(
  SELECT DISTINCT ISBN FROM BOOKS_ORDER
);

SELECT NAME
FROM CLIENT NATURAL JOIN ORDERS
WHERE IDORDER IN (
  SELECT IDORDER
  FROM BOOKS_ORDER
  WHERE AMOUNT>1
);
-- dbext:type=SQLITE:dbname=social.db

/* Delete the tables if they already exist */
drop table if exists Highschooler;
drop table if exists Friend;
drop table if exists Likes;

/* Create the schema for our tables */
create table Highschooler(
	ID int primary key, 
	name text, 
	grade int);
create table Friend(
	student_id int, 
	friend_id int,
	primary key (student_id,friend_id),
	foreign key(student_id) references Highschooler(ID),
	foreign key (friend_id) references Highschooler(ID)
);
create table Likes(
	student_id int, 
	liked_id int,
	primary key (student_id, liked_id),
        foreign key (liked_id) references Highschooler(ID),
        foreign key (student_id) references Highschooler(ID)
);

/* Populate the tables with our data */
insert into Highschooler values (1510, '<PERSON>', 9);
insert into Highschooler values (1689, '<PERSON>', 9);
insert into Highschooler values (1381, '<PERSON>', 9);
insert into Highschooler values (1709, '<PERSON>', 9);
insert into Highschooler values (1101, '<PERSON>', 10);
insert into Highschooler values (1782, '<PERSON>', 10);
insert into Highschooler values (1468, '<PERSON>', 10);
insert into Highschooler values (1641, '<PERSON>', 10);
insert into Highschooler values (1247, '<PERSON>', 11);
insert into Highschooler values (1316, '<PERSON>', 11);
insert into Highschooler values (1911, '<PERSON>', 11);
insert into Highschooler values (1501, '<PERSON>', 11);
insert into Highschooler values (1304, 'Jordan', 12);
insert into Highschooler values (1025, '<PERSON>', 12);
insert into Highschooler values (1934, 'Kyle', 12);
insert into Highschooler values (1661, 'Logan', 12);

insert into Friend values (1510, 1381);
insert into Friend values (1510, 1689);
insert into Friend values (1689, 1709);
insert into Friend values (1381, 1247);
insert into Friend values (1709, 1247);
insert into Friend values (1689, 1782);
insert into Friend values (1782, 1468);
insert into Friend values (1782, 1316);
insert into Friend values (1782, 1304);
insert into Friend values (1468, 1101);
insert into Friend values (1468, 1641);
insert into Friend values (1101, 1641);
insert into Friend values (1247, 1911);
insert into Friend values (1247, 1501);
insert into Friend values (1911, 1501);
insert into Friend values (1501, 1934);
insert into Friend values (1316, 1934);
insert into Friend values (1934, 1304);
insert into Friend values (1304, 1661);
insert into Friend values (1661, 1025);

insert into Likes values(1689, 1709);
insert into Likes values(1709, 1689);
insert into Likes values(1782, 1709);
insert into Likes values(1911, 1247);
insert into Likes values(1247, 1468);
insert into Likes values(1641, 1468);
insert into Likes values(1316, 1304);
insert into Likes values(1501, 1934);
insert into Likes values(1934, 1501);
insert into Likes values(1025, 1101);

PRAGMA foreign_keys = ON;


CREATE TABLE "member" (
    "Member_ID" text,
    "Name" text,
    "Nationality" text,
    "Role" text,
    PRIMARY KEY ("Member_ID")
);



INSERT INTO  "member" VALUES ("1","<PERSON><PERSON><PERSON><PERSON>","Uruguay","<PERSON> Violin");
INSERT INTO  "member" VALUES ("2","<PERSON>","Argentina","Violin");
INSERT INTO  "member" VALUES ("3","<PERSON>éctor <PERSON>","Argentina","Violin");
INSERT INTO  "member" VALUES ("4","<PERSON><PERSON><PERSON>","Argentina","Violin");
INSERT INTO  "member" VALUES ("5","<PERSON> Espil","Argentina","Viola");
INSERT INTO  "member" VALUES ("6","<PERSON>","United States","Viola");
INSERT INTO  "member" VALUES ("7","<PERSON> Burke","United States","Viola");
INSERT INTO  "member" VALUES ("8","<PERSON>","United States","<PERSON><PERSON>");
INSERT INTO  "member" VALUES ("9","<PERSON>","United States","<PERSON><PERSON>");
INSERT INTO  "member" VALUES ("10","<PERSON>","United States","Bass");
INSERT INTO  "member" VALUES ("11","Joseph Bunn","United States","Bass");

CREATE TABLE "performance" (
    "Performance_ID" real,
    "Date" text,
    "Host" text,
    "Location" text,
    "Attendance" int,
    PRIMARY KEY ("Performance_ID")
);

INSERT INTO  "performance" VALUES (1,"February 2","Boston Bruins","TD Garden","165");
INSERT INTO  "performance" VALUES (2,"February 4","New York Rangers","Madison Square Garden","1820");
INSERT INTO  "performance" VALUES (3,"February 5","Atlanta Thrashers","Verizon Center","1878");
INSERT INTO  "performance" VALUES (4,"February 7","Pittsburgh Penguins","Verizon Center","1877");
INSERT INTO  "performance" VALUES (5,"February 10","Montreal Canadiens","Bell Centre","2173");
INSERT INTO  "performance" VALUES (6,"February 11","Ottawa Senators","Scotiabank Place","1982");


CREATE TABLE "member_attendance" (
    "Member_ID" int,
    "Performance_ID" int,
    "Num_of_Pieces" int,
    PRIMARY KEY ("Member_ID","Performance_ID"),
    FOREIGN KEY ("Member_ID") REFERENCES `member`("Member_ID"),
    FOREIGN KEY ("Performance_ID") REFERENCES `performance`("Performance_ID")
);

INSERT INTO  "member_attendance" VALUES (11,3,2);
INSERT INTO  "member_attendance" VALUES (7,2,3);
INSERT INTO  "member_attendance" VALUES (4,6,4);
INSERT INTO  "member_attendance" VALUES (2,1,1);
INSERT INTO  "member_attendance" VALUES (3,1,1);
INSERT INTO  "member_attendance" VALUES (4,3,2);
INSERT INTO  "member_attendance" VALUES (5,1,2);
INSERT INTO  "member_attendance" VALUES (1,4,4);


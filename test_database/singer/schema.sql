PRAGMA foreign_keys = ON;

CREATE TABLE "singer" (
"<PERSON>_<PERSON>" int,
"Name" text,
"Birth_Year" real,
"Net_Worth_Millions" real,
"Citizenship" text,
PRIMARY KEY ("<PERSON>_<PERSON>")
);

CREATE TABLE "song" (
"<PERSON>_<PERSON>" int,
"Title" text,
"<PERSON>_<PERSON>" int,
"Sales" real,
"Highest_Position" real,
<PERSON>IMAR<PERSON> ("<PERSON>_<PERSON>"),
<PERSON><PERSON><PERSON><PERSON><PERSON> ("<PERSON>_<PERSON>") REFERENCES `singer`("<PERSON>_<PERSON>")
);

INSERT INTO  "singer" VALUES (1,"<PERSON><PERSON><PERSON>","1944","30.0","France");
INSERT INTO  "singer" VALUES (2,"<PERSON>","1948","28.8","United States");
INSERT INTO  "singer" VALUES (3,"<PERSON> Walton","1949","26.3","United States");
INSERT INTO  "singer" VALUES (4,"<PERSON> Fontbona","1942","17.4","Chile");
INSERT INTO  "singer" VALUES (5,"<PERSON> Mars","1940","17.8","United States");
INSERT INTO  "singer" VALUES (6,"<PERSON>","1953","17","Australia");
INSERT INTO  "singer" VALUES (7,"<PERSON>ne Klatten","1962","14.3","Germany");
INSERT INTO  "singer" VALUES (8,"Abigail Johnson","1961","12.7","United States");

INSERT INTO  "song" VALUES ("1","Do They Know It's Christmas",1,"1094000","1");
INSERT INTO  "song" VALUES ("2","F**k It (I Don't Want You Back)",1,"552407","1");
INSERT INTO  "song" VALUES ("3","Cha Cha Slide",2,"351421","1");
INSERT INTO  "song" VALUES ("4","Call on Me",4,"335000","1");
INSERT INTO  "song" VALUES ("5","Yeah",2,"300000","1");
INSERT INTO  "song" VALUES ("6","All This Time",6,"292000","1");
INSERT INTO  "song" VALUES ("7","Left Outside Alone",5,"275000","3");
INSERT INTO  "song" VALUES ("8","Mysterious Girl",7,"261000","1");


<?xml version="1.0"?>
<dialects>
    <dialect type="ORACLE">
        <procedure name="GetTweet">
            <statement name="getTweet">
                SELECT * FROM "tweets" WHERE id = ?
            </statement>
        </procedure>
        <procedure name="GetTweetsFromFollowing">
            <statement name="getFollowing">
                SELECT f2 FROM "follows" WHERE f1 = ? AND ROWNUM &lt;= 20
            </statement>
            <statement name="getTweets">
                SELECT * FROM "tweets" WHERE uuid IN (??)
            </statement>
        </procedure>        
        <procedure name="GetFollowers">
            <statement name="getFollowers">
                SELECT f2 FROM "followers" WHERE f1 = ? AND ROWNUM &lt;= 20
            </statement>
            <statement name="getFollowerNames">
                SELECT uuid, name FROM "user_profiles" WHERE uuid IN (??)
            </statement>
        </procedure>
        <procedure name="GetUserTweets">
            <statement name="getTweets">
                SELECT * FROM "tweets" WHERE uuid = ? AND ROWNUM &lt;= 10
            </statement>
        </procedure>
        <procedure name="InsertTweet">
            <statement name="insertTweet">
                INSERT INTO "added_tweets" VALUES (tweet_idseq.nextval, ?, ?, ?)
            </statement>
        </procedure>
    </dialect>
</dialects>
